{"name": "@clara/edoc", "private": true, "version": "1.0.0", "type": "module", "packageManager": "pnpm@10.12.1", "scripts": {"build": "docusaurus build --out-dir dist", "dev": "docusaurus start", "lint": "pnpm lint:js && pnpm lint:css", "lint:js": "eslint .", "lint:css": "stylelint \"**/*.{css,less,scss}\"", "lint:fix": "pnpm lint:js --fix && pnpm lint:css --fix", "serve": "docusaurus serve --dir dist", "clear": "docusaurus clear", "typecheck": "tsc -b && tsc --noEmit", "docusaurus": "<PERSON>cusaurus", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids"}, "keywords": [], "author": "<PERSON><PERSON>", "dependencies": {"@clara/dsl": "workspace:*", "@clara/tslib": "workspace:*", "@docusaurus/core": "catalog:", "@docusaurus/preset-classic": "catalog:", "@docusaurus/theme-search-algolia": "catalog:", "@mdx-js/react": "catalog:", "clsx": "catalog:", "prism-react-renderer": "catalog:", "react": "catalog:", "react-dom": "catalog:", "redocusaurus": "catalog:"}, "devDependencies": {"@docusaurus/module-type-aliases": "catalog:", "@docusaurus/tsconfig": "catalog:", "@docusaurus/types": "catalog:", "@eslint/js": "catalog:", "@types/node": "catalog:", "@types/react": "catalog:", "@types/react-dom": "catalog:", "@typescript-eslint/eslint-plugin": "catalog:", "@typescript-eslint/parser": "catalog:", "eslint": "catalog:", "eslint-config-prettier": "catalog:", "eslint-import-resolver-typescript": "catalog:", "eslint-plugin-import": "catalog:", "eslint-plugin-jsonc": "catalog:", "eslint-plugin-no-unsanitized": "catalog:", "eslint-plugin-prettier": "catalog:", "eslint-plugin-react": "catalog:", "eslint-plugin-react-compiler": "catalog:", "eslint-plugin-react-hooks": "catalog:", "eslint-plugin-react-perf": "catalog:", "eslint-plugin-react-refresh": "catalog:", "prettier": "catalog:", "stylelint": "catalog:", "stylelint-config-clean-order": "catalog:", "stylelint-config-standard": "catalog:", "stylelint-config-standard-scss": "catalog:", "stylelint-order": "catalog:", "stylelint-prettier": "catalog:", "typescript": "catalog:", "typescript-eslint": "catalog:"}, "prettier": "@clara/config/prettier/base.js", "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 3 chrome version", "last 3 firefox version", "last 5 safari version"]}, "engines": {"node": ">=22.0"}}